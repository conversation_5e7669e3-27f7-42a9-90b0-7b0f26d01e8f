package com.ruoyi.project.student.controller;

import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletResponse;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import org.springframework.validation.annotation.Validated;
import com.ruoyi.framework.aspectj.lang.annotation.Log;
import com.ruoyi.framework.aspectj.lang.enums.BusinessType;
import com.ruoyi.project.student.domain.StudentInfo;
import com.ruoyi.project.student.service.IStudentInfoService;
import com.ruoyi.common.utils.poi.ExcelUtil;
import com.ruoyi.common.utils.DateUtils;
import com.ruoyi.framework.web.controller.BaseController;
import com.ruoyi.framework.web.domain.AjaxResult;
import com.ruoyi.framework.web.page.TableDataInfo;

/**
 * 学生信息Controller
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
@RestController
@RequestMapping("/student/info")
public class StudentInfoController extends BaseController
{
    @Autowired
    private IStudentInfoService studentInfoService;

    /**
     * 查询学生信息列表
     */
    @PreAuthorize("@ss.hasPermi('student:info:list')")
    @GetMapping("/list")
    public TableDataInfo list(StudentInfo studentInfo)
    {
        startPage();
        List<StudentInfo> list = studentInfoService.selectStudentInfoList(studentInfo);
        return getDataTable(list);
    }

    /**
     * 导出学生信息列表
     */
    @PreAuthorize("@ss.hasPermi('student:info:export')")
    @Log(title = "学生信息", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, StudentInfo studentInfo)
    {
        List<StudentInfo> list = studentInfoService.selectStudentInfoList(studentInfo);
        ExcelUtil<StudentInfo> util = new ExcelUtil<StudentInfo>(StudentInfo.class);
        util.exportExcel(response, list, "学生信息数据");
    }

    /**
     * 获取学生信息详细信息
     */
    @PreAuthorize("@ss.hasPermi('student:info:query')")
    @GetMapping(value = "/{studentId}")
    public AjaxResult getInfo(@PathVariable("studentId") Long studentId)
    {
        return success(studentInfoService.selectStudentInfoByStudentId(studentId));
    }

    /**
     * 新增学生信息
     */
    @PreAuthorize("@ss.hasPermi('student:info:add')")
    @Log(title = "学生信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@Validated @RequestBody StudentInfo studentInfo)
    {
        if (!studentInfoService.checkStudentNumberUnique(studentInfo))
        {
            return error("新增学生'" + studentInfo.getStudentName() + "'失败，学号已存在");
        }
        return toAjax(studentInfoService.insertStudentInfo(studentInfo));
    }

    /**
     * 修改学生信息
     */
    @PreAuthorize("@ss.hasPermi('student:info:edit')")
    @Log(title = "学生信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@Validated @RequestBody StudentInfo studentInfo)
    {
        if (!studentInfoService.checkStudentNumberUnique(studentInfo))
        {
            return error("修改学生'" + studentInfo.getStudentName() + "'失败，学号已存在");
        }
        return toAjax(studentInfoService.updateStudentInfo(studentInfo));
    }

    /**
     * 删除学生信息
     */
    @PreAuthorize("@ss.hasPermi('student:info:remove')")
    @Log(title = "学生信息", businessType = BusinessType.DELETE)
    @DeleteMapping("/{studentIds}")
    public AjaxResult remove(@PathVariable Long[] studentIds)
    {
        return toAjax(studentInfoService.deleteStudentInfoByStudentIds(studentIds));
    }

    /**
     * 导入学生信息数据
     */
    @Log(title = "学生信息", businessType = BusinessType.IMPORT)
    @PreAuthorize("@ss.hasPermi('student:info:import')")
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<StudentInfo> util = new ExcelUtil<StudentInfo>(StudentInfo.class);
        List<StudentInfo> studentInfoList = util.importExcel(file.getInputStream());
        String operName = getUsername();
        String message = studentInfoService.importStudentInfo(studentInfoList, updateSupport, operName);
        return success(message);
    }

    /**
     * 下载导入模板
     */
    @PostMapping("/importTemplate")
    public void importTemplate(HttpServletResponse response)
    {
        ExcelUtil<StudentInfo> util = new ExcelUtil<StudentInfo>(StudentInfo.class);
        util.importTemplateExcel(response, "学生信息数据");
    }

    /**
     * 更新岗位能力达标阈值
     */
    @PreAuthorize("@ss.hasPermi('student:info:edit')")
    @Log(title = "学生信息", businessType = BusinessType.UPDATE)
    @PostMapping("/updateThreshold")
    public AjaxResult updateThreshold(@RequestBody Map<String, Object> params)
    {
        Integer threshold = (Integer) params.get("threshold");
        if (threshold == null || threshold < 1) {
            return error("阈值必须大于等于1");
        }

        // 更新所有学生的阈值
        List<StudentInfo> studentList = studentInfoService.selectStudentInfoList(new StudentInfo());
        int updateCount = 0;
        for (StudentInfo student : studentList) {
            student.setPositionCompetencyThreshold(threshold);
            student.setUpdateBy(getUsername());
            student.setUpdateTime(DateUtils.getNowDate());
            updateCount += studentInfoService.updateStudentInfo(student);
        }

        return success("成功更新 " + updateCount + " 名学生的岗位能力达标阈值");
    }
}