package com.ruoyi.project.student.mapper;

import java.util.List;
import com.ruoyi.project.student.domain.StudentInfo;

/**
 * 学生信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-01-01
 */
public interface StudentInfoMapper 
{
    /**
     * 查询学生信息
     * 
     * @param studentId 学生信息主键
     * @return 学生信息
     */
    public StudentInfo selectStudentInfoByStudentId(Long studentId);

    /**
     * 查询学生信息列表
     * 
     * @param studentInfo 学生信息
     * @return 学生信息集合
     */
    public List<StudentInfo> selectStudentInfoList(StudentInfo studentInfo);

    /**
     * 新增学生信息
     * 
     * @param studentInfo 学生信息
     * @return 结果
     */
    public int insertStudentInfo(StudentInfo studentInfo);

    /**
     * 修改学生信息
     * 
     * @param studentInfo 学生信息
     * @return 结果
     */
    public int updateStudentInfo(StudentInfo studentInfo);

    /**
     * 删除学生信息
     * 
     * @param studentId 学生信息主键
     * @return 结果
     */
    public int deleteStudentInfoByStudentId(Long studentId);

    /**
     * 批量删除学生信息
     * 
     * @param studentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteStudentInfoByStudentIds(Long[] studentIds);

    /**
     * 校验学号是否唯一
     *
     * @param studentNumber 学号
     * @return 结果
     */
    public StudentInfo checkStudentNumberUnique(String studentNumber);

    /**
     * 批量更新岗位能力达标阈值
     *
     * @param threshold 阈值
     * @return 结果
     */
    public int updatePositionCompetencyThreshold(Integer threshold);
}