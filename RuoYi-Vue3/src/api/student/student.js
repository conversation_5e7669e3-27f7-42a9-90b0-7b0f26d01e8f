import request from '@/utils/request'

// 查询学生信息列表
export function listStudentInfo(query) {
  return request({
    url: '/student/info/list',
    method: 'get',
    params: query
  })
}

// 查询学生信息详细
export function getStudentInfo(studentId) {
  return request({
    url: '/student/info/' + studentId,
    method: 'get'
  })
}

// 新增学生信息
export function addStudentInfo(data) {
  return request({
    url: '/student/info',
    method: 'post',
    data: data
  })
}

// 修改学生信息
export function updateStudentInfo(data) {
  return request({
    url: '/student/info',
    method: 'put',
    data: data
  })
}

// 删除学生信息
export function delStudentInfo(studentId) {
  return request({
    url: '/student/info/' + studentId,
    method: 'delete'
  })
}

// 更新岗位能力达标阈值
export function updateThreshold(threshold) {
  return request({
    url: '/student/info/updateThreshold',
    method: 'post',
    data: { threshold }
  })
}

// 导入学生信息
export function importStudentInfo(data) {
  return request({
    url: '/student/info/importData',
    method: 'post',
    data: data
  })
}

// 下载学生信息导入模板
export function downloadStudentInfoTemplate() {
  return request({
    url: '/student/info/importTemplate',
    method: 'post'
  })
} 