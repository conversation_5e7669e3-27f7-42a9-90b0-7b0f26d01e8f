<template>
  <div class="app-container">
    <div class="profile-container">
      <!-- 左侧学生列表 -->
      <div class="student-list-panel">
        <div class="panel-header">
          <h3>学生列表</h3>
          <el-input
            v-model="searchKeyword"
            placeholder="请输入学生姓名"
            clearable
            @input="handleSearch"
            prefix-icon="Search"
          />
        </div>
        
        <div class="student-groups" v-loading="loading">
          <div v-if="!loading && filteredStudentGroups.length === 0" class="no-data">
            <el-empty description="暂无学生数据" />
          </div>
          <div 
            v-for="group in filteredStudentGroups" 
            :key="group.major" 
            class="student-group"
          >
            <div class="group-header" @click="toggleGroup(group.major)">
              <span class="group-title">{{ group.major }}</span>
              <el-icon :class="{ 'is-expanded': expandedGroups.includes(group.major) }">
                <ArrowDown />
              </el-icon>
            </div>
            
            <div 
              v-show="expandedGroups.includes(group.major)" 
              class="group-content"
            >
              <div 
                v-for="student in group.students" 
                :key="student.studentId"
                :class="['student-item', { active: selectedStudent?.studentId === student.studentId }]"
                @click="selectStudent(student)"
              >
                <el-avatar :size="32" :src="student.avatar || ''">
                  {{ student.studentName?.charAt(0) }}
                </el-avatar>
                <span class="student-name">{{ student.studentName }}</span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧内容区域 -->
      <div class="content-panel">
        <!-- Tab栏 -->
        <div class="tab-header">
          <div class="tab-bar">
            <div 
              :class="['tab-item', { active: activeTab === 'profile' }]"
              @click="switchTab('profile')"
            >
              学生个体画像
            </div>
            <div 
              :class="['tab-item', { active: activeTab === 'evaluation' }]"
              @click="switchTab('evaluation')"
            >
              评价情况
            </div>
          </div>
          
          <el-button 
            type="primary" 
            @click="handleThresholdSetting"
          >
            岗位能力达标阈值设置
          </el-button>
        </div>

        <!-- 个人档案区域 -->
        <div class="personal-profile-section">
          <div class="profile-content-wrapper">
            <!-- 左侧个人档案 -->
            <PersonalProfile :student="selectedStudent" />
            
            <!-- 右侧岗位能力达标程度 -->
            <CompetencyLevel :student="selectedStudent" />
          </div>
        </div>

        <!-- 职业能力分析区域 -->
        <div v-if="activeTab === 'profile'" class="career-analysis-section">
          <div class="analysis-grid">
            <CareerAbilityAnalysis :student="selectedStudent" />
            <ScoreSummary :student="selectedStudent" />
            <PositionAnalysis :student="selectedStudent" />
          </div>
        </div>
      </div>
    </div>

    <!-- 阈值设置弹框 -->
    <ThresholdSettingDialog
      v-model="showThresholdDialog"
      @success="handleThresholdSuccess"
    />
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { listStudentInfo } from '@/api/student/student'
import { ArrowDown } from '@element-plus/icons-vue'
import PersonalProfile from './components/PersonalProfile.vue'
import CompetencyLevel from './components/CompetencyLevel.vue'
import CareerAbilityAnalysis from './components/CareerAbilityAnalysis.vue'
import PositionAnalysis from './components/PositionAnalysis.vue'
import ScoreSummary from './components/ScoreSummary.vue'
import ThresholdSettingDialog from './components/ThresholdSettingDialog.vue'

export default {
  name: "PersonalProfilePage",
  components: {
    ArrowDown,
    PersonalProfile,
    CompetencyLevel,
    CareerAbilityAnalysis,
    PositionAnalysis,
    ScoreSummary,
    ThresholdSettingDialog
  },
  setup() {
    // 响应式数据
    const searchKeyword = ref('')
    const activeTab = ref('profile')
    const selectedStudent = ref(null)
    const expandedGroups = ref([])
    const studentList = ref([])
    const loading = ref(false)
    const showThresholdDialog = ref(false)

    // 按专业分组的学生数据
    const studentGroups = computed(() => {
      const groups = {}
      
      studentList.value.forEach(student => {
        const major = student.major || '未分类'
        if (!groups[major]) {
          groups[major] = []
        }
        groups[major].push(student)
      })
      
      return Object.keys(groups).map(major => ({
        major,
        students: groups[major]
      }))
    })

    // 过滤后的学生分组（根据搜索关键词）
    const filteredStudentGroups = computed(() => {
      if (!searchKeyword.value) {
        return studentGroups.value
      }
      
      return studentGroups.value.map(group => ({
        ...group,
        students: group.students.filter(student => 
          student.studentName?.includes(searchKeyword.value)
        )
      })).filter(group => group.students.length > 0)
    })

    // 方法
    const loadStudentList = async () => {
      loading.value = true
      try {
        // 传递很大的页面大小来获取所有学生数据
        const response = await listStudentInfo({
          pageNum: 1,
          pageSize: 9999
        })
        studentList.value = response.rows || []
        
        // 默认展开第一个专业组
        if (studentGroups.value.length > 0) {
          expandedGroups.value = [studentGroups.value[0].major]
        }
      } catch (error) {
        console.error('加载学生列表失败:', error)
        // 可以在这里添加错误提示
      } finally {
        loading.value = false
      }
    }



    const handleSearch = () => {
      // 搜索时展开所有包含匹配学生的专业组
      const matchedMajors = new Set()
      filteredStudentGroups.value.forEach(group => {
        if (group.students.length > 0) {
          matchedMajors.add(group.major)
        }
      })
      expandedGroups.value = Array.from(matchedMajors)
    }

    const toggleGroup = (major) => {
      const index = expandedGroups.value.indexOf(major)
      if (index > -1) {
        expandedGroups.value.splice(index, 1)
      } else {
        expandedGroups.value.push(major)
      }
    }

    const selectStudent = (student) => {
      selectedStudent.value = student
    }

    const switchTab = (tab) => {
      activeTab.value = tab
    }

    const handleThresholdSetting = () => {
      showThresholdDialog.value = true
    }

    const handleThresholdSuccess = () => {
      // 阈值设置成功后可以重新加载学生列表或其他操作
      console.log('阈值设置成功')
    }

    // 生命周期
    onMounted(() => {
      loadStudentList()
    })

    return {
      searchKeyword,
      activeTab,
      selectedStudent,
      expandedGroups,
      studentList,
      loading,
      showThresholdDialog,
      studentGroups,
      filteredStudentGroups,
      handleSearch,
      toggleGroup,
      selectStudent,
      switchTab,
      handleThresholdSetting,
      handleThresholdSuccess
    }
  }
}
</script>

<style scoped>
.profile-container {
  display: flex;
  height: calc(100vh - 120px);
  gap: 20px;
}

/* 左侧学生列表面板 */
.student-list-panel {
  width: 300px;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

.panel-header {
  padding: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.panel-header h3 {
  margin: 0 0 12px 0;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}

.student-groups {
  flex: 1;
  overflow-y: auto;
  padding: 8px 0;
}

.student-group {
  margin-bottom: 8px;
}

.group-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  /* background: #f8f9fa; */
  cursor: pointer;
  transition: background-color 0.2s;
}

.group-header:hover {
  background: #f8f9fa;
}

.group-title {
  font-weight: 500;
  color: #495057;
}

.group-header .el-icon {
  transition: transform 0.2s;
}

.group-header .el-icon.is-expanded {
  transform: rotate(180deg);
}

.group-content {
  background: #fff;
}

.student-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  cursor: pointer;
  transition: background-color 0.2s;
  border-left: 3px solid transparent;
}

.student-item:hover {
  background: #f8f9fa;
}

.student-item.active {
  background: #e3f2fd;
  border-left-color: #2196f3;
}

.student-name {
  font-size: 14px;
  color: #333;
}

/* 右侧内容面板 */
.content-panel {
  flex: 1;
  background: #fff;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  flex-direction: column;
}

/* Tab栏 */
.tab-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 20px;
  border-bottom: 1px solid #f0f0f0;
}

.tab-bar {
  display: flex;
  gap: 32px;
}

.tab-item {
  padding: 8px 0;
  cursor: pointer;
  font-size: 14px;
  color: #666;
  position: relative;
  transition: color 0.2s;
}

.tab-item:hover {
  color: #2196f3;
}

.tab-item.active {
  color: #2196f3;
  font-weight: 500;
}

.tab-item.active::after {
  content: '';
  position: absolute;
  bottom: -16px;
  left: 0;
  right: 0;
  height: 2px;
  background: #2196f3;
}

/* 个人档案区域 */
.personal-profile-section {
  padding: 20px;
  background: #fff;
  
}

.profile-content-wrapper {
  display: flex;
  gap: 40px;
  align-items: flex-start;
  border: 1px solid #e5e5e5;
  border-radius: 8px;
  padding: 20px;

}

.profile-content-wrapper > * {
  flex: 1;
}

.no-data {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 200px;
}

/* 职业能力分析区域 */
.career-analysis-section {
  padding: 20px;
  background: #fff;
}

.analysis-grid {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 20px;
  align-items: start;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .profile-container {
    flex-direction: column;
    height: auto;
  }

  .student-list-panel {
    width: 100%;
    height: 300px;
  }

  .content-panel {
    height: 600px;
  }

  .profile-content-wrapper {
    flex-direction: column;
    gap: 20px;
  }

  .analysis-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 768px) {
  .profile-content-wrapper {
    flex-direction: column;
    gap: 16px;
  }

  .profile-content-wrapper > * {
    flex: none;
  }

  .analysis-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}
</style> 