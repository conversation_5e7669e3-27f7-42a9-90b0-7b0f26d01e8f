<template>
  <el-dialog
    v-model="visible"
    title="岗位能力达标阈值设置"
    width="400px"
    :close-on-click-modal="false"
    @close="handleClose"
  >
    <div class="threshold-setting-content">
      <div class="setting-item">
        <label class="setting-label">未达标能力数 ≥</label>
        <el-input-number
          v-model="threshold"
          :min="1"
          :max="10"
          :step="1"
          controls-position="right"
          class="threshold-input"
        />
        <span class="setting-unit">项，视为不达标。</span>
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleConfirm">确认</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script>
import { ref, watch } from 'vue'
import { updateThreshold } from '@/api/student/student'
import { ElMessage } from 'element-plus'

export default {
  name: 'ThresholdSettingDialog',
  props: {
    modelValue: {
      type: Boolean,
      default: false
    }
  },
  emits: ['update:modelValue', 'success'],
  setup(props, { emit }) {
    const visible = ref(false)
    const threshold = ref(2) // 默认值为2
    const loading = ref(false)

    // 监听props变化
    watch(() => props.modelValue, (newVal) => {
      visible.value = newVal
    })

    // 监听visible变化
    watch(visible, (newVal) => {
      emit('update:modelValue', newVal)
    })

    const handleClose = () => {
      visible.value = false
    }

    const handleCancel = () => {
      visible.value = false
    }

    const handleConfirm = async () => {
      if (threshold.value < 1) {
        ElMessage.warning('阈值必须大于等于1')
        return
      }

      try {
        loading.value = true
        await updateThreshold(threshold.value)
        ElMessage.success('岗位能力达标阈值设置成功')
        emit('success')
        visible.value = false
      } catch (error) {
        console.error('更新阈值失败:', error)
        ElMessage.error('设置失败，请重试')
      } finally {
        loading.value = false
      }
    }

    return {
      visible,
      threshold,
      loading,
      handleClose,
      handleCancel,
      handleConfirm
    }
  }
}
</script>

<style scoped>
.threshold-setting-content {
  padding: 20px 0;
}

.setting-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.setting-label {
  color: #333;
  white-space: nowrap;
}

.threshold-input {
  width: 80px;
}

.setting-unit {
  color: #333;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
